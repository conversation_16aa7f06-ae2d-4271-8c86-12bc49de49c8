import { Link as RouterLink } from "@tanstack/react-router";
import { useTranslation } from "react-i18next";
import { z } from "zod/v4";
import { <PERSON><PERSON>, <PERSON>lex, Link } from "@/components";
import { Form, Password } from "@/components/form";
import { Notification } from "@/components/notification";

export type PasswordResetFormData = {
    password: string;
    confirmPassword: string;
};

const schema: z.ZodSchema<PasswordResetFormData> = z
    .object({
        password: z.string().min(6, "Password must be at least 6 characters"),
        confirmPassword: z.string().min(6, "Password must be at least 6 characters"),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: "Passwords don't match",
        path: ["confirmPassword"],
    });

type Props = {
    onSubmit: (data: PasswordResetFormData) => void;
    error?: string;
    isLoading?: boolean;
};

export function PasswordResetForm({ onSubmit, error, isLoading }: Props) {
    const { t } = useTranslation();

    return (
        <Form schema={schema} onSubmit={onSubmit} defaultValues={{ password: "", confirmPassword: "" }}>
            <Flex direction="column" gap="4">
                {error && (
                    <div className="mb-4">
                        <Notification type="error">{error}</Notification>
                    </div>
                )}
                <Password<PasswordResetFormData>
                    name="password"
                    label={t("passwordResetForm.newPassword")}
                    placeholder={t("passwordResetForm.newPasswordPlaceholder")}
                    disabled={isLoading}
                    autoComplete="new-password"
                />
                <Password<PasswordResetFormData>
                    name="confirmPassword"
                    label={t("passwordResetForm.confirmPassword")}
                    placeholder={t("passwordResetForm.confirmPasswordPlaceholder")}
                    disabled={isLoading}
                    autoComplete="new-password"
                />
                <Button type="submit" loading={isLoading}>
                    {t("passwordResetForm.resetPassword")}
                </Button>
                <Flex mt="4" justify="center" gap="1">
                    <Flex justify="end">
                        <Link asChild size={"2"}>
                            <RouterLink to={"/login"}>{t("passwordResetForm.backToLogin")}</RouterLink>
                        </Link>
                    </Flex>
                </Flex>
            </Flex>
        </Form>
    );
}
